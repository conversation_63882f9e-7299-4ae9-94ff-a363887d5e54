import Phaser from 'phaser';
import { GameConfig } from '../config/GameConfig.js';

export class UIScene extends Phaser.Scene {
    constructor() {
        super({ key: 'UIScene' });
        this.score = 0;
        this.level = 1;
        this.playerCount = 1;
        this.scoreText = null;
        this.levelText = null;
        this.healthBars = [];
    }

    init(data) {
        this.level = data.level || 1;
        this.playerCount = data.players || 1;
    }

    create() {
        // 创建UI背景
        this.createUIBackground();
        
        // 创建分数显示
        this.createScoreDisplay();
        
        // 创建关卡显示
        this.createLevelDisplay();
        
        // 创建生命值显示
        this.createHealthDisplay();
        
        // 创建暂停按钮
        this.createPauseButton();
        
        // 监听游戏事件
        this.setupEventListeners();
    }

    createUIBackground() {
        // 创建顶部UI背景条
        const uiBar = this.add.rectangle(
            GameConfig.GAME_WIDTH / 2, 
            25, 
            GameConfig.GAME_WIDTH, 
            50, 
            0x000000, 
            0.7
        );
        uiBar.setScrollFactor(0);
    }

    createScoreDisplay() {
        this.scoreText = this.add.text(20, 15, '分数: 0', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold'
        });
        this.scoreText.setScrollFactor(0);
    }

    createLevelDisplay() {
        this.levelText = this.add.text(GameConfig.GAME_WIDTH / 2, 15, `关卡: ${this.level}`, {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold'
        }).setOrigin(0.5, 0);
        this.levelText.setScrollFactor(0);
    }

    createHealthDisplay() {
        const startX = GameConfig.GAME_WIDTH - 200;
        const y = 15;

        for (let i = 0; i < this.playerCount; i++) {
            const playerLabel = this.add.text(startX + (i * 100), y, `P${i + 1}:`, {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial'
            });
            playerLabel.setScrollFactor(0);

            // 创建生命值图标
            const healthIcons = [];
            for (let j = 0; j < GameConfig.PLAYER.MAX_HEALTH; j++) {
                const heart = this.add.text(
                    startX + (i * 100) + 30 + (j * 15), 
                    y + 20, 
                    '♥', 
                    {
                        fontSize: '16px',
                        fill: '#ff0000',
                        fontFamily: 'Arial'
                    }
                );
                heart.setScrollFactor(0);
                healthIcons.push(heart);
            }
            
            this.healthBars.push(healthIcons);
        }
    }

    createPauseButton() {
        const pauseButton = this.add.text(GameConfig.GAME_WIDTH - 20, 15, '⏸', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(1, 0);
        
        pauseButton.setScrollFactor(0);
        pauseButton.setInteractive();
        pauseButton.on('pointerdown', () => {
            this.scene.get('GameScene').togglePause();
        });
    }

    setupEventListeners() {
        // 监听分数更新事件
        this.events.on('updateScore', this.updateScore, this);
        
        // 监听生命值更新事件
        this.events.on('updateHealth', this.updateHealth, this);
        
        // 监听关卡更新事件
        this.events.on('updateLevel', this.updateLevel, this);
    }

    updateScore(points) {
        this.score += points;
        this.scoreText.setText(`分数: ${this.score}`);
        
        // 添加分数增加动画
        this.tweens.add({
            targets: this.scoreText,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            ease: 'Power2'
        });
    }

    updateHealth(playerIndex, health) {
        if (playerIndex < this.healthBars.length) {
            const healthIcons = this.healthBars[playerIndex];
            
            healthIcons.forEach((heart, index) => {
                if (index < health) {
                    heart.setTint(0xff0000); // 红色表示有生命
                    heart.setAlpha(1);
                } else {
                    heart.setTint(0x666666); // 灰色表示失去生命
                    heart.setAlpha(0.5);
                }
            });
        }
    }

    updateLevel(newLevel) {
        this.level = newLevel;
        this.levelText.setText(`关卡: ${this.level}`);
        
        // 添加关卡更新动画
        this.tweens.add({
            targets: this.levelText,
            scaleX: 1.3,
            scaleY: 1.3,
            duration: 300,
            yoyo: true,
            ease: 'Power2'
        });
    }

    // 显示游戏暂停界面
    showPauseMenu() {
        const centerX = GameConfig.GAME_WIDTH / 2;
        const centerY = GameConfig.GAME_HEIGHT / 2;

        // 暂停背景
        const pauseBackground = this.add.rectangle(
            centerX, centerY, 
            400, 300, 
            0x000000, 0.8
        );
        pauseBackground.setScrollFactor(0);

        // 暂停文本
        const pauseText = this.add.text(centerX, centerY - 50, '游戏暂停', {
            fontSize: '32px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        pauseText.setScrollFactor(0);

        // 继续按钮
        const resumeButton = this.add.text(centerX, centerY, '继续游戏', {
            fontSize: '24px',
            fill: '#00ff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        resumeButton.setScrollFactor(0);
        resumeButton.setInteractive();
        resumeButton.on('pointerdown', () => {
            this.hidePauseMenu();
            this.scene.get('GameScene').togglePause();
        });

        // 返回主菜单按钮
        const menuButton = this.add.text(centerX, centerY + 50, '返回主菜单', {
            fontSize: '24px',
            fill: '#ff0000',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        menuButton.setScrollFactor(0);
        menuButton.setInteractive();
        menuButton.on('pointerdown', () => {
            this.scene.stop('GameScene');
            this.scene.stop('UIScene');
            this.scene.start('MenuScene');
        });

        // 保存暂停菜单元素以便隐藏
        this.pauseMenuElements = [pauseBackground, pauseText, resumeButton, menuButton];
    }

    hidePauseMenu() {
        if (this.pauseMenuElements) {
            this.pauseMenuElements.forEach(element => element.destroy());
            this.pauseMenuElements = null;
        }
    }

    // 显示游戏结束界面
    showGameOver(victory = false) {
        const centerX = GameConfig.GAME_WIDTH / 2;
        const centerY = GameConfig.GAME_HEIGHT / 2;

        // 游戏结束背景
        const gameOverBackground = this.add.rectangle(
            centerX, centerY, 
            500, 400, 
            victory ? 0x004400 : 0x440000, 0.9
        );
        gameOverBackground.setScrollFactor(0);

        // 游戏结束文本
        const gameOverText = this.add.text(
            centerX, centerY - 100, 
            victory ? '恭喜通关！' : '游戏结束', 
            {
                fontSize: '36px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontStyle: 'bold'
            }
        ).setOrigin(0.5);
        gameOverText.setScrollFactor(0);

        // 最终分数
        const finalScoreText = this.add.text(
            centerX, centerY - 50, 
            `最终分数: ${this.score}`, 
            {
                fontSize: '24px',
                fill: '#ffffff',
                fontFamily: 'Arial'
            }
        ).setOrigin(0.5);
        finalScoreText.setScrollFactor(0);

        // 重新开始按钮
        const restartButton = this.add.text(centerX, centerY + 20, '重新开始', {
            fontSize: '24px',
            fill: '#00ff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        restartButton.setScrollFactor(0);
        restartButton.setInteractive();
        restartButton.on('pointerdown', () => {
            this.scene.stop('GameScene');
            this.scene.stop('UIScene');
            this.scene.start('GameScene', { players: this.playerCount });
        });

        // 返回主菜单按钮
        const menuButton = this.add.text(centerX, centerY + 70, '返回主菜单', {
            fontSize: '24px',
            fill: '#ff0000',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        menuButton.setScrollFactor(0);
        menuButton.setInteractive();
        menuButton.on('pointerdown', () => {
            this.scene.stop('GameScene');
            this.scene.stop('UIScene');
            this.scene.start('MenuScene');
        });
    }
}
