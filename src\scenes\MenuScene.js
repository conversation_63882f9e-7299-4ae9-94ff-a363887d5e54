import Phaser from 'phaser';
import { GameConfig } from '../config/GameConfig.js';

export class MenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MenuScene' });
        this.selectedOption = 0;
        this.menuOptions = [];
    }

    create() {
        const centerX = this.cameras.main.width / 2;
        const centerY = this.cameras.main.height / 2;

        // 背景渐变效果
        this.createBackground();

        // 游戏标题
        this.add.text(centerX, centerY - 200, '雪人兄弟', {
            fontSize: '64px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 4
        }).setOrigin(0.5);

        // 副标题
        this.add.text(centerX, centerY - 140, 'Snow Brothers', {
            fontSize: '24px',
            fill: '#cccccc',
            fontFamily: 'Arial',
            fontStyle: 'italic'
        }).setOrigin(0.5);

        // 创建菜单选项
        this.createMenuOptions(centerX, centerY);

        // 创建控制说明
        this.createControlsInfo(centerX);

        // 设置键盘输入
        this.setupInput();

        // 添加动画效果
        this.createAnimations();
    }

    createBackground() {
        // 创建雪花背景效果
        for (let i = 0; i < 50; i++) {
            const x = Phaser.Math.Between(0, this.cameras.main.width);
            const y = Phaser.Math.Between(0, this.cameras.main.height);
            const snowflake = this.add.circle(x, y, Phaser.Math.Between(2, 6), 0xffffff, 0.6);
            
            // 添加飘落动画
            this.tweens.add({
                targets: snowflake,
                y: snowflake.y + this.cameras.main.height,
                duration: Phaser.Math.Between(3000, 8000),
                repeat: -1,
                ease: 'Linear',
                delay: Phaser.Math.Between(0, 5000)
            });
        }
    }

    createMenuOptions(centerX, centerY) {
        const options = [
            { text: '单人游戏', action: () => this.startSinglePlayer() },
            { text: '双人游戏', action: () => this.startMultiPlayer() },
            { text: '设置', action: () => this.openSettings() },
            { text: '关于', action: () => this.showAbout() }
        ];

        this.menuOptions = [];

        options.forEach((option, index) => {
            const y = centerY - 20 + (index * 60);
            const menuItem = this.add.text(centerX, y, option.text, {
                fontSize: '32px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);

            menuItem.setInteractive();
            menuItem.on('pointerover', () => this.selectOption(index));
            menuItem.on('pointerdown', option.action);

            this.menuOptions.push({
                text: menuItem,
                action: option.action
            });
        });

        // 高亮第一个选项
        this.updateSelection();
    }

    createControlsInfo(centerX) {
        const controlsY = this.cameras.main.height - 120;
        
        this.add.text(centerX, controlsY, '游戏控制', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.add.text(centerX, controlsY + 30, '玩家1: WASD移动 + 空格投掷', {
            fontSize: '16px',
            fill: '#cccccc',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        this.add.text(centerX, controlsY + 50, '玩家2: 方向键移动 + Enter投掷', {
            fontSize: '16px',
            fill: '#cccccc',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        this.add.text(centerX, controlsY + 80, '按 ↑↓ 选择菜单，Enter 确认', {
            fontSize: '14px',
            fill: '#999999',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    setupInput() {
        this.cursors = this.input.keyboard.createCursorKeys();
        this.enterKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ENTER);
        
        // 监听键盘事件
        this.input.keyboard.on('keydown-UP', () => {
            this.selectOption(this.selectedOption - 1);
        });
        
        this.input.keyboard.on('keydown-DOWN', () => {
            this.selectOption(this.selectedOption + 1);
        });
        
        this.input.keyboard.on('keydown-ENTER', () => {
            this.confirmSelection();
        });
    }

    createAnimations() {
        // 标题闪烁动画
        const title = this.children.list.find(child => 
            child.type === 'Text' && child.text === '雪人兄弟'
        );
        
        if (title) {
            this.tweens.add({
                targets: title,
                alpha: 0.7,
                duration: 1500,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }

    selectOption(index) {
        // 确保索引在有效范围内
        this.selectedOption = Phaser.Math.Clamp(index, 0, this.menuOptions.length - 1);
        this.updateSelection();
    }

    updateSelection() {
        this.menuOptions.forEach((option, index) => {
            if (index === this.selectedOption) {
                option.text.setTint(0x00ff00);
                option.text.setScale(1.1);
            } else {
                option.text.clearTint();
                option.text.setScale(1.0);
            }
        });
    }

    confirmSelection() {
        this.menuOptions[this.selectedOption].action();
    }

    startSinglePlayer() {
        this.scene.start('GameScene', { players: 1 });
    }

    startMultiPlayer() {
        this.scene.start('GameScene', { players: 2 });
    }

    openSettings() {
        // TODO: 实现设置界面
        console.log('打开设置');
    }

    showAbout() {
        // TODO: 实现关于界面
        console.log('显示关于信息');
    }
}
