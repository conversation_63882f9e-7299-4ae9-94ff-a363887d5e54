import Phaser from 'phaser';
import { GameConfig } from '../config/GameConfig.js';

export class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.players = [];
        this.enemies = [];
        this.snowballs = [];
        this.platforms = null;
        this.currentLevel = 1;
        this.gameData = null;
    }

    init(data) {
        this.gameData = data;
        this.playerCount = data.players || 1;
    }

    create() {
        // 启动UI场景
        this.scene.launch('UIScene', { 
            level: this.currentLevel,
            players: this.playerCount 
        });

        // 创建世界边界
        this.physics.world.setBounds(0, 0, GameConfig.GAME_WIDTH, GameConfig.GAME_HEIGHT);

        // 创建关卡
        this.createLevel();

        // 创建玩家
        this.createPlayers();

        // 创建敌人
        this.createEnemies();

        // 设置输入控制
        this.setupInput();

        // 设置碰撞检测
        this.setupCollisions();

        // 设置相机
        this.setupCamera();

        // 游戏状态
        this.gameState = GameConfig.GAME_STATES.PLAYING;
    }

    createLevel() {
        // 创建平台组
        this.platforms = this.physics.add.staticGroup();

        // 创建地面
        for (let x = 0; x < GameConfig.GAME_WIDTH; x += 32) {
            this.platforms.create(x + 16, GameConfig.GAME_HEIGHT - 16, 'ground');
        }

        // 创建一些平台（简单的关卡设计）
        this.createPlatform(200, 600, 6);
        this.createPlatform(600, 500, 4);
        this.createPlatform(100, 400, 3);
        this.createPlatform(700, 350, 5);
        this.createPlatform(300, 250, 4);
    }

    createPlatform(x, y, length) {
        for (let i = 0; i < length; i++) {
            this.platforms.create(x + (i * 32), y, 'ground');
        }
    }

    createPlayers() {
        // 创建玩家1
        const player1 = this.physics.add.sprite(100, 500, 'player1');
        player1.setBounce(0.2);
        player1.setCollideWorldBounds(true);
        player1.setData('playerNumber', 1);
        player1.setData('health', GameConfig.PLAYER.MAX_HEALTH);
        player1.setData('invincible', false);
        player1.setData('lastThrow', 0);
        
        this.players.push(player1);

        // 创建玩家2（如果是双人游戏）
        if (this.playerCount === 2) {
            const player2 = this.physics.add.sprite(150, 500, 'player2');
            player2.setBounce(0.2);
            player2.setCollideWorldBounds(true);
            player2.setData('playerNumber', 2);
            player2.setData('health', GameConfig.PLAYER.MAX_HEALTH);
            player2.setData('invincible', false);
            player2.setData('lastThrow', 0);
            
            this.players.push(player2);
        }
    }

    createEnemies() {
        this.enemies = this.physics.add.group();

        // 创建一些敌人
        const enemyPositions = [
            { x: 400, y: 500 },
            { x: 700, y: 400 },
            { x: 200, y: 300 },
            { x: 800, y: 200 }
        ];

        enemyPositions.forEach(pos => {
            const enemy = this.enemies.create(pos.x, pos.y, 'enemy1');
            enemy.setBounce(0.1);
            enemy.setCollideWorldBounds(true);
            enemy.setData('stunned', false);
            enemy.setData('direction', Phaser.Math.Between(0, 1) ? 1 : -1);
            enemy.setVelocityX(GameConfig.ENEMY.SPEED * enemy.getData('direction'));
        });
    }

    setupInput() {
        // 玩家1控制
        this.player1Keys = this.input.keyboard.addKeys({
            left: Phaser.Input.Keyboard.KeyCodes.A,
            right: Phaser.Input.Keyboard.KeyCodes.D,
            up: Phaser.Input.Keyboard.KeyCodes.W,
            down: Phaser.Input.Keyboard.KeyCodes.S,
            throw: Phaser.Input.Keyboard.KeyCodes.SPACE
        });

        // 玩家2控制（如果存在）
        if (this.playerCount === 2) {
            this.player2Keys = this.input.keyboard.addKeys({
                left: Phaser.Input.Keyboard.KeyCodes.LEFT,
                right: Phaser.Input.Keyboard.KeyCodes.RIGHT,
                up: Phaser.Input.Keyboard.KeyCodes.UP,
                down: Phaser.Input.Keyboard.KeyCodes.DOWN,
                throw: Phaser.Input.Keyboard.KeyCodes.ENTER
            });
        }

        // 暂停键
        this.pauseKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
        this.pauseKey.on('down', this.togglePause, this);
    }

    setupCollisions() {
        // 玩家与平台碰撞
        this.players.forEach(player => {
            this.physics.add.collider(player, this.platforms);
        });

        // 敌人与平台碰撞
        this.physics.add.collider(this.enemies, this.platforms);

        // 敌人与世界边界碰撞时转向
        this.enemies.children.entries.forEach(enemy => {
            enemy.body.onWorldBounds = true;
        });

        this.physics.world.on('worldbounds', (event, body) => {
            if (this.enemies.children.entries.includes(body.gameObject)) {
                const enemy = body.gameObject;
                const direction = enemy.getData('direction') * -1;
                enemy.setData('direction', direction);
                enemy.setVelocityX(GameConfig.ENEMY.SPEED * direction);
            }
        });
    }

    setupCamera() {
        // 设置相机跟随玩家
        if (this.players.length > 0) {
            this.cameras.main.startFollow(this.players[0]);
            this.cameras.main.setLerp(0.1, 0.1);
        }
    }

    update() {
        if (this.gameState !== GameConfig.GAME_STATES.PLAYING) {
            return;
        }

        // 更新玩家
        this.updatePlayers();

        // 更新敌人
        this.updateEnemies();

        // 更新雪球
        this.updateSnowballs();

        // 检查游戏结束条件
        this.checkGameEnd();
    }

    updatePlayers() {
        this.players.forEach((player, index) => {
            const keys = index === 0 ? this.player1Keys : this.player2Keys;
            this.updatePlayer(player, keys);
        });
    }

    updatePlayer(player, keys) {
        // 水平移动
        if (keys.left.isDown) {
            player.setVelocityX(-GameConfig.PLAYER.SPEED);
        } else if (keys.right.isDown) {
            player.setVelocityX(GameConfig.PLAYER.SPEED);
        } else {
            player.setVelocityX(0);
        }

        // 跳跃
        if (keys.up.isDown && player.body.touching.down) {
            player.setVelocityY(GameConfig.PLAYER.JUMP_VELOCITY);
        }

        // 投掷雪球
        if (Phaser.Input.Keyboard.JustDown(keys.throw)) {
            this.throwSnowball(player);
        }
    }

    updateEnemies() {
        this.enemies.children.entries.forEach(enemy => {
            if (!enemy.getData('stunned')) {
                // 简单的AI：遇到平台边缘时转向
                if (enemy.body.touching.down && !enemy.body.blocked.down) {
                    const direction = enemy.getData('direction') * -1;
                    enemy.setData('direction', direction);
                    enemy.setVelocityX(GameConfig.ENEMY.SPEED * direction);
                }
            }
        });
    }

    updateSnowballs() {
        // 清理超出边界的雪球
        this.snowballs = this.snowballs.filter(snowball => {
            if (snowball.x < 0 || snowball.x > GameConfig.GAME_WIDTH || 
                snowball.y < 0 || snowball.y > GameConfig.GAME_HEIGHT) {
                snowball.destroy();
                return false;
            }
            return true;
        });
    }

    throwSnowball(player) {
        const currentTime = this.time.now;
        const lastThrow = player.getData('lastThrow');
        
        if (currentTime - lastThrow < GameConfig.PLAYER.SNOWBALL_COOLDOWN) {
            return;
        }

        player.setData('lastThrow', currentTime);

        const snowball = this.physics.add.sprite(player.x, player.y - 10, 'snowball');
        snowball.setVelocityX(GameConfig.PLAYER.SNOWBALL_SPEED * (player.flipX ? -1 : 1));
        snowball.setCollideWorldBounds(true);
        snowball.setBounce(0.8);

        this.snowballs.push(snowball);

        // 雪球与平台碰撞
        this.physics.add.collider(snowball, this.platforms);

        // 雪球与敌人碰撞
        this.physics.add.overlap(snowball, this.enemies, this.snowballHitEnemy, null, this);
    }

    snowballHitEnemy(snowball, enemy) {
        snowball.destroy();
        this.snowballs = this.snowballs.filter(s => s !== snowball);

        if (!enemy.getData('stunned')) {
            enemy.setData('stunned', true);
            enemy.setTint(0x0000ff);
            enemy.setVelocityX(0);

            // 设置恢复计时器
            this.time.delayedCall(GameConfig.ENEMY.STUN_TIME, () => {
                if (enemy.active) {
                    enemy.setData('stunned', false);
                    enemy.clearTint();
                    enemy.setVelocityX(GameConfig.ENEMY.SPEED * enemy.getData('direction'));
                }
            });
        }
    }

    checkGameEnd() {
        // 检查是否所有敌人都被消灭
        if (this.enemies.children.entries.length === 0) {
            this.levelComplete();
        }

        // 检查玩家是否全部死亡
        const alivePlayers = this.players.filter(player => player.getData('health') > 0);
        if (alivePlayers.length === 0) {
            this.gameOver();
        }
    }

    levelComplete() {
        this.gameState = GameConfig.GAME_STATES.LEVEL_COMPLETE;
        // TODO: 显示关卡完成界面
        console.log('关卡完成！');
    }

    gameOver() {
        this.gameState = GameConfig.GAME_STATES.GAME_OVER;
        // TODO: 显示游戏结束界面
        console.log('游戏结束！');
    }

    togglePause() {
        if (this.gameState === GameConfig.GAME_STATES.PLAYING) {
            this.gameState = GameConfig.GAME_STATES.PAUSED;
            this.physics.pause();
        } else if (this.gameState === GameConfig.GAME_STATES.PAUSED) {
            this.gameState = GameConfig.GAME_STATES.PLAYING;
            this.physics.resume();
        }
    }
}
