// 游戏配置常量
export const GameConfig = {
    // 游戏尺寸
    GAME_WIDTH: 1024,
    GAME_HEIGHT: 768,
    
    // 物理参数
    GRAVITY: 800,
    DEBUG_PHYSICS: false,
    
    // 玩家参数
    PLAYER: {
        SPEED: 200,
        JUMP_VELOCITY: -400,
        MAX_HEALTH: 3,
        INVINCIBLE_TIME: 2000, // 无敌时间（毫秒）
        SNOWBALL_SPEED: 300,
        SNOWBALL_COOLDOWN: 500 // 投掷冷却时间
    },
    
    // 敌人参数
    ENEMY: {
        SPEED: 100,
        STUN_TIME: 3000, // 被击晕时间
        KICK_VELOCITY: 400 // 被踢飞的速度
    },
    
    // 关卡参数
    LEVEL: {
        TILE_SIZE: 32,
        MAX_LEVELS: 10
    },
    
    // 道具参数
    POWERUPS: {
        FIRE_BALL: {
            duration: 10000, // 持续时间
            speed_multiplier: 1.5
        },
        RAPID_FIRE: {
            duration: 8000,
            cooldown_reduction: 0.3
        },
        SHIELD: {
            duration: 15000,
            max_hits: 3
        }
    },
    
    // 控制键配置
    CONTROLS: {
        PLAYER1: {
            LEFT: 'A',
            RIGHT: 'D', 
            UP: 'W',
            DOWN: 'S',
            THROW: 'SPACE'
        },
        PLAYER2: {
            LEFT: 'LEFT',
            RIGHT: 'RIGHT',
            UP: 'UP', 
            DOWN: 'DOWN',
            THROW: 'ENTER'
        }
    },
    
    // 音频配置
    AUDIO: {
        MASTER_VOLUME: 0.7,
        SFX_VOLUME: 0.8,
        MUSIC_VOLUME: 0.6
    },
    
    // 游戏状态
    GAME_STATES: {
        MENU: 'menu',
        PLAYING: 'playing',
        PAUSED: 'paused',
        GAME_OVER: 'game_over',
        LEVEL_COMPLETE: 'level_complete'
    },
    
    // 颜色配置
    COLORS: {
        PRIMARY: '#3498db',
        SECONDARY: '#e74c3c',
        SUCCESS: '#2ecc71',
        WARNING: '#f39c12',
        DANGER: '#e74c3c',
        WHITE: '#ffffff',
        BLACK: '#000000'
    }
};
