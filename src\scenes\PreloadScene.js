import Phaser from 'phaser';

export class PreloadScene extends Phaser.Scene {
    constructor() {
        super({ key: 'PreloadScene' });
    }

    preload() {
        // 创建加载进度条
        this.createLoadingBar();
        
        // 加载游戏资源
        this.loadAssets();
        
        // 监听加载进度
        this.load.on('progress', this.updateLoadingBar, this);
        this.load.on('complete', this.loadComplete, this);
    }

    createLoadingBar() {
        const centerX = this.cameras.main.width / 2;
        const centerY = this.cameras.main.height / 2;
        
        // 背景
        this.add.rectangle(centerX, centerY - 50, 400, 50, 0x222222);
        this.add.rectangle(centerX, centerY - 50, 396, 46, 0x333333);
        
        // 进度条
        this.loadingBar = this.add.rectangle(centerX - 196, centerY - 50, 0, 42, 0x00ff00);
        this.loadingBar.setOrigin(0, 0.5);
        
        // 加载文本
        this.loadingText = this.add.text(centerX, centerY, '加载中... 0%', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        
        // 游戏标题
        this.add.text(centerX, centerY - 150, '雪人兄弟', {
            fontSize: '48px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontStyle: 'bold'
        }).setOrigin(0.5);
    }

    updateLoadingBar(progress) {
        this.loadingBar.width = 392 * progress;
        this.loadingText.setText(`加载中... ${Math.round(progress * 100)}%`);
    }

    loadAssets() {
        // 创建简单的占位符图像（后续会替换为真实资源）
        this.createPlaceholderAssets();
        
        // 加载音频文件（占位符）
        // this.load.audio('bgm', ['assets/sounds/bgm.mp3']);
        // this.load.audio('jump', ['assets/sounds/jump.wav']);
        // this.load.audio('throw', ['assets/sounds/throw.wav']);
        // this.load.audio('hit', ['assets/sounds/hit.wav']);
    }

    createPlaceholderAssets() {
        // 创建雪人角色占位符
        this.createPlayerSprite('player1', 0x87CEEB); // 浅蓝色
        this.createPlayerSprite('player2', 0xFFB6C1); // 浅粉色
        
        // 创建敌人占位符
        this.createEnemySprite('enemy1', 0xFF6347); // 番茄红
        
        // 创建冰球占位符
        this.createSnowballSprite();
        
        // 创建地面瓦片占位符
        this.createTileSprite();
        
        // 创建道具占位符
        this.createPowerupSprites();
    }

    createPlayerSprite(key, color) {
        const graphics = this.add.graphics();
        graphics.fillStyle(color);
        graphics.fillRoundedRect(0, 0, 32, 32, 8);
        graphics.fillStyle(0xFFFFFF);
        graphics.fillCircle(8, 10, 3); // 左眼
        graphics.fillCircle(24, 10, 3); // 右眼
        graphics.fillStyle(0xFF4500);
        graphics.fillTriangle(16, 16, 12, 20, 20, 20); // 鼻子
        graphics.generateTexture(key, 32, 32);
        graphics.destroy();
    }

    createEnemySprite(key, color) {
        const graphics = this.add.graphics();
        graphics.fillStyle(color);
        graphics.fillRoundedRect(0, 0, 28, 28, 6);
        graphics.fillStyle(0x000000);
        graphics.fillCircle(8, 10, 2); // 左眼
        graphics.fillCircle(20, 10, 2); // 右眼
        graphics.generateTexture(key, 28, 28);
        graphics.destroy();
    }

    createSnowballSprite() {
        const graphics = this.add.graphics();
        graphics.fillStyle(0xFFFFFF);
        graphics.fillCircle(8, 8, 8);
        graphics.lineStyle(2, 0xE0E0E0);
        graphics.strokeCircle(8, 8, 6);
        graphics.generateTexture('snowball', 16, 16);
        graphics.destroy();
    }

    createTileSprite() {
        const graphics = this.add.graphics();
        graphics.fillStyle(0x8B4513);
        graphics.fillRect(0, 0, 32, 32);
        graphics.lineStyle(2, 0x654321);
        graphics.strokeRect(0, 0, 32, 32);
        graphics.generateTexture('ground', 32, 32);
        graphics.destroy();
    }

    createPowerupSprites() {
        // 火焰球道具
        const fireGraphics = this.add.graphics();
        fireGraphics.fillStyle(0xFF4500);
        fireGraphics.fillCircle(12, 12, 12);
        fireGraphics.fillStyle(0xFF6347);
        fireGraphics.fillCircle(12, 12, 8);
        fireGraphics.generateTexture('powerup_fire', 24, 24);
        fireGraphics.destroy();
        
        // 快速投掷道具
        const rapidGraphics = this.add.graphics();
        rapidGraphics.fillStyle(0x00FF00);
        rapidGraphics.fillCircle(12, 12, 12);
        rapidGraphics.fillStyle(0x32CD32);
        rapidGraphics.fillCircle(12, 12, 8);
        rapidGraphics.generateTexture('powerup_rapid', 24, 24);
        rapidGraphics.destroy();
        
        // 护盾道具
        const shieldGraphics = this.add.graphics();
        shieldGraphics.fillStyle(0x0000FF);
        shieldGraphics.fillCircle(12, 12, 12);
        shieldGraphics.fillStyle(0x4169E1);
        shieldGraphics.fillCircle(12, 12, 8);
        shieldGraphics.generateTexture('powerup_shield', 24, 24);
        shieldGraphics.destroy();
    }

    loadComplete() {
        this.loadingText.setText('加载完成！');
        
        // 延迟一秒后进入主菜单
        this.time.delayedCall(1000, () => {
            this.scene.start('MenuScene');
        });
    }
}
