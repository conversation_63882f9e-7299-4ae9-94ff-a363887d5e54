import Phaser from 'phaser';
import { GameConfig } from './config/GameConfig.js';
import { PreloadScene } from './scenes/PreloadScene.js';
import { MenuScene } from './scenes/MenuScene.js';
import { GameScene } from './scenes/GameScene.js';
import { UIScene } from './scenes/UIScene.js';

// 游戏配置
const config = {
    type: Phaser.AUTO,
    width: GameConfig.GAME_WIDTH,
    height: GameConfig.GAME_HEIGHT,
    parent: 'game-container',
    backgroundColor: '#2c3e50',
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: GameConfig.GRAVITY },
            debug: GameConfig.DEBUG_PHYSICS
        }
    },
    scene: [PreloadScene, MenuScene, GameScene, UIScene],
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {
            width: 800,
            height: 600
        },
        max: {
            width: 1200,
            height: 900
        }
    },
    render: {
        pixelArt: true,
        antialias: false
    }
};

// 创建游戏实例
class SnowBrothersGame {
    constructor() {
        this.game = null;
        this.init();
    }

    init() {
        // 清除加载提示
        const gameContainer = document.getElementById('game-container');
        gameContainer.innerHTML = '';
        
        // 创建游戏
        this.game = new Phaser.Game(config);
        
        // 添加全局事件监听
        this.setupGlobalEvents();
    }

    setupGlobalEvents() {
        // 窗口失焦时暂停游戏
        window.addEventListener('blur', () => {
            if (this.game && this.game.scene.isActive('GameScene')) {
                this.game.scene.pause('GameScene');
            }
        });

        // 窗口获得焦点时恢复游戏
        window.addEventListener('focus', () => {
            if (this.game && this.game.scene.isPaused('GameScene')) {
                this.game.scene.resume('GameScene');
            }
        });
    }
}

// 启动游戏
window.addEventListener('load', () => {
    new SnowBrothersGame();
});
